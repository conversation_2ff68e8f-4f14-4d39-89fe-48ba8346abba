import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_color.dart';
import '../../../../core/constants/app_dimens.dart';
import '../../../../core/enums/user_role.dart';
import '../../../../domain/entities/user_entity.dart';
import '../../../router/navigation_extensions.dart';
import '../../../widgets/common/common.dart';
import '../../../controllers/auth_controller.dart';
import '../../../controllers/auth_state.dart';

/// Tab cá nhân - hiển thị thông tin người dùng và cài đặt
class ProfileTab extends ConsumerStatefulWidget {
  final UserEntity user;

  const ProfileTab({
    super.key,
    required this.user,
  });

  @override
  ConsumerState<ProfileTab> createState() => _ProfileTabState();
}

class _ProfileTabState extends ConsumerState<ProfileTab>
    with AutomaticKeepAliveClientMixin {
  
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    // Listen to auth state changes for logout
    ref.listen<AuthState>(authControllerProvider, (previous, next) {
      next.when(
        (isLoading, isLoggedIn, user, failure, isEmailValid, isPasswordValid) {
          // Handle default state
        },
        initial: () {},
        loading: () {},
        authenticated: (user) {},
        unauthenticated: (failure) {
          // User has been logged out, navigate to login
          if (mounted) {
            context.goToLogin();
          }
        },
        error: (failure) {
          // Handle logout error but still navigate to login
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Đã xảy ra lỗi khi đăng xuất'),
                backgroundColor: AppColors.error,
              ),
            );
            context.goToLogin();
          }
        },
      );
    });
    
    return Scaffold(
      backgroundColor: AppColors.surfaceColor,
      appBar: AppBar(
        title: const Text(
          'Cá nhân',
          style: TextStyle(
            color: AppColors.textWhite,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.primaryColor,
        elevation: 0,
        automaticallyImplyLeading: false,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: AppDimens.paddingAllLg,
          child: Column(
            children: [
              
              // Logout Button using BottomButton
              _buildLogoutButton(),
              
              AppDimens.h24, // Extra padding at bottom
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUserInfoCard() {
    return Container(
      padding: AppDimens.paddingAllLg,
      decoration: BoxDecoration(
        color: AppColors.backgroundColor,
        borderRadius: AppDimens.borderRadius16,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Avatar
          Container(
            width: AppDimens.avatarXl,
            height: AppDimens.avatarXl,
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: [
                  AppColors.secondaryColor,
                  AppColors.buttonColor,
                ],
              ),
            ),
            child: Icon(
              Icons.person,
              size: AppDimens.iconXL,
              color: AppColors.textWhite,
            ),
          ),
          AppDimens.w16,
          
          // User Details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.user.fullName,
                  style: TextStyle(
                    fontSize: AppDimens.fontXL,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                AppDimens.h4,
                Text(
                  widget.user.role.displayName,
                  style: TextStyle(
                    fontSize: AppDimens.fontMD,
                    color: AppColors.secondaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                AppDimens.h4,
                Text(
                  widget.user.phoneNumber,
                  style: TextStyle(
                    fontSize: AppDimens.fontSM,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  Widget _buildLogoutButton() {
    return CommonButton(
      title: 'Đăng xuất',
      onPressed: _logout,
      backgroundColor: AppColors.error,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.logout,
            color: AppColors.textWhite,
          ),
          AppDimens.w8,
          Text(
            'Đăng xuất',
            style: TextStyle(
              fontSize: AppDimens.fontMD,
              fontWeight: FontWeight.w600,
              color: AppColors.textWhite,
            ),
          ),
        ],
      ),
    );
  }

  void _logout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Đăng xuất'),
        content: const Text('Bạn có chắc chắn muốn đăng xuất không?\n\nTất cả dữ liệu đã lưu sẽ bị xóa.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Hủy'),
          ),
          CommonButton(
            title: 'Đăng xuất',
            onPressed: () async {
              Navigator.pop(context);
              
              // Show loading indicator
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) => const Center(
                  child: CircularProgressIndicator(),
                ),
              );
              
              // Call AuthController logout to clear all data
              await ref.read(authControllerProvider.notifier).logout();
              
              // Close loading dialog
              if (context.mounted) {
                Navigator.pop(context);
              }
            },
            backgroundColor: AppColors.error,
            width: 100,
            height: 40,
          ),
        ],
      ),
    );
  }
} 