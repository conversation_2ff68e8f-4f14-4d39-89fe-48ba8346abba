import 'package:flutter/material.dart';
import '../../../../core/theme/app_color.dart';
import '../../../../core/constants/app_dimens.dart';

class ModernDashboardCards extends StatelessWidget {
  const ModernDashboardCards({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: AppDimens.marginHorizontalLg.copyWith(top: AppDimens.spacingLG),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Chức năng chính',
            style: TextStyle(
              fontSize: AppDimens.fontLG,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          AppDimens.h16,
          Row(
            children: [
              Expanded(
                child: _buildDashboardCard(
                  icon: Icons.payment,
                  title: 'Vay trả góp',
                  subtitle: 'Quản lý khoản vay',
                  count: '24',
                  gradient: [AppColors.success, AppColors.success.withValues(alpha: 0.6)],
                  onTap: () => _onInstallmentLoanTap(context),
                ),
              ),
              AppDimens.w12,
              Expanded(
                child: _buildDashboardCard(
                  icon: Icons.support_agent,
                  title: 'Trợ lý bán hàng',
                  subtitle: 'Thư viện điện tử',
                  count: '12',
                  gradient: [AppColors.info, AppColors.info.withValues(alpha: 0.6)],
                  onTap: () => _onSalesAssistantTap(context),
                ),
              ),
            ],
          ),
          AppDimens.h12,
          Row(
            children: [
              Expanded(
                child: _buildDashboardCard(
                  icon: Icons.attach_money,
                  title: 'Thu tiền',
                  subtitle: 'Khách hàng',
                  count: '8',
                  gradient: [AppColors.error, AppColors.error.withValues(alpha: 0.6)],
                  onTap: () => _onCollectMoneyTap(context),
                ),
              ),
              AppDimens.w12,
              Expanded(
                child: _buildDashboardCard(
                  icon: Icons.analytics,
                  title: 'Báo cáo',
                  subtitle: 'Thống kê chi tiết',
                  count: '5',
                  gradient: [AppColors.secondaryColor, AppColors.secondaryColor.withValues(alpha: 0.6)],
                  onTap: () => _onReportTap(context),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required String count,
    required List<Color> gradient,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: AppDimens.borderRadius16,
        child: Container(
          height: 120,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: gradient,
            ),
            borderRadius: AppDimens.borderRadius16,
            boxShadow: [
              BoxShadow(
                color: gradient[0].withValues(alpha: 0.3),
                blurRadius: 12,
                offset: const Offset(0, 6),
              ),
            ],
          ),
          child: Stack(
            children: [
              // Background pattern
              Positioned(
                right: -20,
                top: -20,
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
              Positioned(
                right: -10,
                bottom: -10,
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.05),
                    shape: BoxShape.circle,
                  ),
                ),
              ),

              // Content
              Padding(
                padding: AppDimens.paddingAllLg,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          padding: AppDimens.paddingAllSm,
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: AppDimens.borderRadius8,
                          ),
                          child: Icon(
                            icon,
                            color: AppColors.textWhite,
                            size: AppDimens.iconSM,
                          ),
                        ),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: AppDimens.spacingSM,
                            vertical: AppDimens.spacingXS,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: AppDimens.borderRadius8,
                          ),
                          child: Text(
                            count,
                            style: TextStyle(
                              fontSize: AppDimens.fontSM,
                              fontWeight: FontWeight.bold,
                              color: AppColors.textWhite,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const Spacer(),
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: AppDimens.fontMD,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textWhite,
                      ),
                    ),
                    AppDimens.h4,
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: AppDimens.fontSM,
                        color: AppColors.textWhite.withValues(alpha: 0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _onInstallmentLoanTap(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Mở chức năng Vay trả góp'),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _onSalesAssistantTap(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Mở chức năng Trợ lý bán hàng'),
        backgroundColor: AppColors.info,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _onCollectMoneyTap(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Mở chức năng Thu tiền khách hàng'),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _onReportTap(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Mở chức năng Báo cáo'),
        backgroundColor: AppColors.secondaryColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
