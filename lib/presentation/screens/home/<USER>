import 'package:flutter/material.dart';
import '../../../core/theme/app_color.dart';
import '../../../core/constants/app_dimens.dart';
import '../../../core/enums/user_role.dart';
import '../../../domain/entities/user_entity.dart';
import 'tabs/home_tab.dart';
import 'tabs/report_tab.dart';
import 'tabs/profile_tab.dart';

/// Màn hình trang chủ mới với BottomNavigationBar và phân quyền theo role
class HomeScreenV2 extends StatefulWidget {
  final UserEntity? initialUser;

  const HomeScreenV2({
    super.key,
    this.initialUser,
  });

  @override
  State<HomeScreenV2> createState() => _HomeScreenV2State();
}

class _HomeScreenV2State extends State<HomeScreenV2>
    with TickerProviderStateMixin {
  late TabController _tabController;
  int _currentIndex = 0;

  // Mock user data - trong thực tế sẽ lấy từ state management
  late UserEntity currentUser;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    // Khởi tạo user data
    currentUser = widget.initialUser ?? _getMockUser();
    
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        setState(() {
          _currentIndex = _tabController.index;
        });
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Lấy mock user data để demo
  UserEntity _getMockUser() {
    return const UserEntity(
      id: '1',
      fullName: 'Nguyễn Văn An',
      role: UserRole.ctv,
      phoneNumber: '**********',
      email: '<EMAIL>',
      unreadNotifications: 5,
      isActive: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: TabBarView(
        controller: _tabController,
        physics: const NeverScrollableScrollPhysics(), // Disable swipe
        children: [
          // Tab 1: Trang chủ
          HomeTab(user: currentUser),
          
          // Tab 2: Báo cáo
          ReportTab(user: currentUser),
          
          // Tab 3: Cá nhân
          ProfileTab(user: currentUser),
        ],
      ),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  Widget _buildBottomNavigationBar() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.backgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Container(
          height: AppDimens.bottomBarHeight,
          padding: AppDimens.paddingHorizontalMd,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildBottomNavItem(
                index: 0,
                icon: Icons.home_outlined,
                activeIcon: Icons.home,
                label: 'Trang chủ',
              ),
              _buildBottomNavItem(
                index: 1,
                icon: Icons.bar_chart_outlined,
                activeIcon: Icons.bar_chart,
                label: 'Báo cáo',
              ),
              _buildBottomNavItem(
                index: 2,
                icon: Icons.person_outline,
                activeIcon: Icons.person,
                label: 'Cá nhân',
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBottomNavItem({
    required int index,
    required IconData icon,
    required IconData activeIcon,
    required String label,
  }) {
    final isActive = _currentIndex == index;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _currentIndex = index;
          _tabController.animateTo(index);
        });
      },
      behavior: HitTestBehavior.translucent,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: AppDimens.animationNormal),
        padding: EdgeInsets.symmetric(
          vertical: AppDimens.spacingSM,
          horizontal: AppDimens.spacingXS,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AnimatedSwitcher(
              duration: const Duration(milliseconds: AppDimens.animationFast),
              child: Icon(
                isActive ? activeIcon : icon,
                key: ValueKey('$index-$isActive'),
                size: AppDimens.iconLG,
                color: isActive ? AppColors.primaryColor : AppColors.textSecondary,
              ),
            ),
            AppDimens.h8,
            AnimatedDefaultTextStyle(
              duration: const Duration(milliseconds: AppDimens.animationNormal),
              style: TextStyle(
                fontSize: AppDimens.fontMD,
                color: isActive ? AppColors.primaryColor : AppColors.textSecondary,
                fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
              ),
              child: Text(label),
            ),
          ],
        ),
      ),
    );
  }
} 