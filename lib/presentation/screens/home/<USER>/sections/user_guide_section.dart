import 'package:flutter/material.dart';
import '../../../../../core/theme/app_color.dart';
import '../../../../../core/constants/app_dimens.dart';
import '../../../../../domain/entities/user_entity.dart';

/// Widget hiển thị hướng dẫn sử dụng App Sale
class UserGuideSection extends StatelessWidget {
  final UserEntity user;

  const UserGuideSection({
    super.key,
    required this.user,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _showGuidesBottomSheet(context),
      child: Container(
        padding: AppDimens.paddingAllMd,
        decoration: BoxDecoration(
          color: AppColors.backgroundColor,
          borderRadius: AppDimens.borderRadius12,
          border: Border.all(
            color: AppColors.surfaceColor,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Guide Icon
            Container(
              width: AppDimens.containerMd,
              height: AppDimens.containerMd,
              decoration: BoxDecoration(
                color: AppColors.info.withValues(alpha: 0.1),
                borderRadius: AppDimens.borderRadius8,
              ),
              child: Icon(
                Icons.help_center_outlined,
                color: AppColors.info,
                size: AppDimens.iconMD,
              ),
            ),
            AppDimens.w12,
            
            // Guide Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Hướng dẫn sử dụng',
                    style: TextStyle(
                      fontSize: AppDimens.fontMD,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  AppDimens.h2,
                  Text(
                    '4 hướng dẫn có sẵn • Nhấn để xem chi tiết',
                    style: TextStyle(
                      fontSize: AppDimens.fontSM,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            
            // Arrow Icon
            Icon(
              Icons.keyboard_arrow_right,
              size: AppDimens.iconMD,
              color: AppColors.textSecondary,
            ),
          ],
        ),
      ),
    );
  }

  void _showGuidesBottomSheet(BuildContext context) {
    final guides = _getUserGuides();
    
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: AppColors.backgroundColor,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(16),
            topRight: Radius.circular(16),
          ),
        ),
        child: Column(
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              margin: AppDimens.paddingVerticalSm,
              decoration: BoxDecoration(
                color: AppColors.surfaceColor,
                borderRadius: AppDimens.borderRadius4,
              ),
            ),
            
            // Header
            Padding(
              padding: AppDimens.paddingHorizontalLg,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Hướng dẫn sử dụng',
                    style: TextStyle(
                      fontSize: AppDimens.fontXL,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(
                      Icons.close,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            
            // Guides List
            Expanded(
              child: ListView.separated(
                padding: AppDimens.paddingAllLg,
                itemCount: guides.length,
                separatorBuilder: (context, index) => AppDimens.h12,
                itemBuilder: (context, index) {
                  return _buildDetailedGuideItem(context, guides[index]);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailedGuideItem(BuildContext context, UserGuide guide) {
    return Container(
      padding: AppDimens.paddingAllMd,
      decoration: BoxDecoration(
        color: AppColors.surfaceColor,
        borderRadius: AppDimens.borderRadius12,
        border: Border.all(
          color: AppColors.surfaceColor,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Guide Icon
          Container(
            width: AppDimens.containerLg,
            height: AppDimens.containerLg,
            decoration: BoxDecoration(
              color: guide.color.withValues(alpha: 0.1),
              borderRadius: AppDimens.borderRadius12,
            ),
            child: Icon(
              guide.icon,
              color: guide.color,
              size: AppDimens.iconLG,
            ),
          ),
          AppDimens.w16,
          
          // Guide Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        guide.title,
                        style: TextStyle(
                          fontSize: AppDimens.fontMD,
                          fontWeight: FontWeight.w600,
                          color: AppColors.textPrimary,
                        ),
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: AppDimens.spacingSM,
                        vertical: AppDimens.spacingXS,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.info.withValues(alpha: 0.1),
                        borderRadius: AppDimens.borderRadius8,
                      ),
                      child: Text(
                        guide.duration,
                        style: TextStyle(
                          fontSize: AppDimens.fontXS,
                          color: AppColors.info,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
                                 AppDimens.h4,
                Text(
                  guide.description,
                  style: TextStyle(
                    fontSize: AppDimens.fontSM,
                    color: AppColors.textSecondary,
                    height: 1.4,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          
          AppDimens.w8,
          Icon(
            Icons.play_circle_outline,
            color: guide.color,
            size: AppDimens.iconMD,
          ),
        ],
      ),
    );
  }

  List<UserGuide> _getUserGuides() {
    return [
      const UserGuide(
        id: 'getting_started',
        title: 'Bắt đầu với App Sale',
        description: 'Hướng dẫn cơ bản về cách sử dụng các tính năng chính của ứng dụng',
        shortDescription: 'Cách sử dụng cơ bản',
        icon: Icons.play_circle_outline,
        color: AppColors.primaryColor,
        duration: '5 phút',
        videoUrl: '',
      ),
      const UserGuide(
        id: 'create_loan',
        title: 'Tạo khoản vay mới',
        description: 'Quy trình tạo và quản lý hồ sơ vay cho khách hàng từ A đến Z',
        shortDescription: 'Quy trình tạo hồ sơ',
        icon: Icons.add_business,
        color: AppColors.success,
        duration: '8 phút',
        videoUrl: '',
      ),
      const UserGuide(
        id: 'customer_management',
        title: 'Quản lý khách hàng',
        description: 'Cách thêm, chỉnh sửa và theo dõi thông tin khách hàng hiệu quả',
        shortDescription: 'Quản lý thông tin KH',
        icon: Icons.people_outline,
        color: AppColors.info,
        duration: '6 phút',
        videoUrl: '',
      ),
      const UserGuide(
        id: 'reports',
        title: 'Xem báo cáo & thống kê',
        description: 'Hướng dẫn sử dụng các tính năng báo cáo và phân tích dữ liệu',
        shortDescription: 'Báo cáo & thống kê',
        icon: Icons.analytics_outlined,
        color: AppColors.warning,
        duration: '7 phút',
        videoUrl: '',
      ),
    ];
  }
}

/// Class đại diện cho một hướng dẫn sử dụng
class UserGuide {
  final String id;
  final String title;
  final String description;
  final String shortDescription; // Added short description for compact view
  final IconData icon;
  final Color color;
  final String duration;
  final String videoUrl;

  const UserGuide({
    required this.id,
    required this.title,
    required this.description,
    required this.shortDescription, // Required short description
    required this.icon,
    required this.color,
    required this.duration,
    required this.videoUrl,
  });
} 