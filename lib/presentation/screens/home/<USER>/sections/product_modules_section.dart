import 'package:flutter/material.dart';
import '../../../../../core/theme/app_color.dart';
import '../../../../../core/constants/app_dimens.dart';
import '../../../../../domain/entities/user_entity.dart';

/// Widget hiển thị grid các module sản phẩm
class ProductModulesSection extends StatelessWidget {
  final UserEntity user;

  const ProductModulesSection({
    super.key,
    required this.user,
  });

  @override
  Widget build(BuildContext context) {
    final modules = _getProductModules();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppDimens.h16,
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: AppDimens.gridCrossAxisCount4,
            crossAxisSpacing: AppDimens.gridCrossAxisSpacing,
            mainAxisSpacing: AppDimens.gridMainAxisSpacing,
            childAspectRatio: AppDimens.gridChildAspectRatio,
          ),
          itemCount: modules.length,
          itemBuilder: (context, index) {
            final module = modules[index];
            return _buildModuleItem(context, module);
          },
        ),
      ],
    );
  }

  Widget _buildModuleItem(BuildContext context, ProductModule module) {
    return GestureDetector(
      onTap: () => _onModuleTap(context, module),
      child: Padding(
        padding: AppDimens.paddingAllXS,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Simple icon container
            Container(
              width: AppDimens.iconXL,
              height: AppDimens.iconXL,
              decoration: BoxDecoration(
                color: AppColors.primaryColor.withValues(alpha: 0.06),
                borderRadius: AppDimens.borderRadius8,
              ),
              child: Icon(
                module.icon,
                color: AppColors.primaryColor,
                size: AppDimens.iconMD,
              ),
            ),
            AppDimens.h4,
            // Text với alignment top
            Expanded(
              child: Text(
                module.name,
                style: TextStyle(
                  fontSize: AppDimens.fontSM,
                  fontWeight: FontWeight.w500,
                  color: AppColors.textPrimary,
                  height: 1.1,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _onModuleTap(BuildContext context, ProductModule module) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Mở module: ${module.name}')),
    );
  }

  List<ProductModule> _getProductModules() {
    return [
      ProductModule(
        id: 'loan',
        name: 'Vay trả góp ngày',
        icon: Icons.credit_card_rounded,
        color: AppColors.primaryColor,
      ),
      ProductModule(
        id: 'assistant',
        name: 'Trợ lý bán hàng',
        icon: Icons.support_agent_rounded,
        color: AppColors.secondaryColor,
      ),
      ProductModule(
        id: 'payment',
        name: 'Thu tiền KH',
        icon: Icons.payments_rounded,
        color: AppColors.success,
      ),
      ProductModule(
        id: 'report',
        name: 'Báo cáo',
        icon: Icons.analytics_rounded,
        color: AppColors.warning,
      ),
      ProductModule(
        id: 'customer',
        name: 'Quản lý KH',
        icon: Icons.people_rounded,
        color: AppColors.info,
      ),
      ProductModule(
        id: 'contract',
        name: 'Hợp đồng',
        icon: Icons.description_rounded,
        color: AppColors.buttonColor,
      ),
      ProductModule(
        id: 'notification',
        name: 'Thông báo',
        icon: Icons.notifications_rounded,
        color: AppColors.error,
      ),
    ];
  }
}

/// Class đại diện cho một module sản phẩm
class ProductModule {
  final String id;
  final String name;
  final IconData icon;
  final Color color;

  const ProductModule({
    required this.id,
    required this.name,
    required this.icon,
    required this.color,
  });
}