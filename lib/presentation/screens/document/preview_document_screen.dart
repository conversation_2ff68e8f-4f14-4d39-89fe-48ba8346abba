import 'dart:io';
import 'package:flutter/material.dart';
import 'package:sales_app/core/enums/document_side.dart';
import 'package:sales_app/core/constants/app_dimens.dart';
import 'package:sales_app/presentation/widgets/common/base_screen.dart';
import 'package:sales_app/presentation/screens/document/widgets/document_frame_painter.dart';

class PreviewDocumentScreen extends StatelessWidget {
  final File image;
  final DocumentSide side;
  final Function(File image) onAccept;

  const PreviewDocumentScreen({
    super.key,
    required this.image,
    required this.side,
    required this.onAccept,
  });

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    const cardRatio = AppDimens.documentPreviewRatio; // Tỷ lệ CCCD
    final cardWidth = size.width * AppDimens.documentPreviewScale;
    final cardHeight = cardWidth / cardRatio;
    final screenHeight = size.height - MediaQuery.of(context).padding.top - kToolbarHeight;
    final frameTop = (screenHeight - cardHeight) / AppDimens.documentPreviewDivisor;

    return BaseScreen(
      title: side == DocumentSide.front ? 'Chụp mặt trước' : 'Chụp mặt sau',
      body: Container(
        color: Colors.black,
        child: Stack(
          fit: StackFit.expand,
          children: [
            // Full screen overlay
            Container(
              color: Colors.black,
            ),

            // Document frame with image
            Positioned(
              top: frameTop,
              left: 0,
              right: 0,
              height: cardHeight,
              child: Stack(
                children: [
                  // Image preview trong khung
                  ClipRect(
                    child: Image.file(
                      image,
                      width: double.infinity,
                      height: double.infinity,
                      fit: BoxFit.cover,
                    ),
                  ),

                  // Viền khung và góc chỉ dẫn
                  CustomPaint(
                    painter: DocumentFramePainter(
                      width: cardWidth,
                      height: cardHeight,
                    ),
                    size: Size(size.width, cardHeight),
                  ),
                ],
              ),
            ),

            // Guidance text
            Positioned(
              top: frameTop + cardHeight + 24,
              left: 0,
              right: 0,
              child: Container(
                margin: AppDimens.marginHorizontalLg,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'Kiểm tra ${side == DocumentSide.front ? "mặt trước" : "mặt sau"} GTTT',
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Vui lòng kiểm tra lại ảnh đã chụp',
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.white.withValues(alpha: 0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Action buttons
            Positioned(
              left: 0,
              right: 0,
              bottom: 48,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Retake button
                  _ActionButton(
                    onTap: () => Navigator.pop(context),
                    icon: Icons.refresh,
                    isOutlined: true,
                    label: 'Chụp lại',
                  ),
                  const SizedBox(width: 32),
                  // Accept button
                  _ActionButton(
                    onTap: () {
                      onAccept(image);
                      Navigator.pop(context);
                      Navigator.pop(context);
                    },
                    icon: Icons.check,
                    label: 'Lấy ảnh này',
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _ActionButton extends StatelessWidget {
  final VoidCallback onTap;
  final IconData icon;
  final bool isOutlined;
  final String? label;

  const _ActionButton({
    required this.onTap,
    required this.icon,
    this.isOutlined = false,
    this.label,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(40),
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: isOutlined ? Colors.transparent : Colors.white,
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.white,
                  width: 4,
                ),
              ),
              child: Icon(
                icon,
                color: isOutlined ? Colors.white : Colors.black,
                size: 32,
              ),
            ),
          ),
          if (label != null) ...[
            const SizedBox(height: 8),
            Text(
              label!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.white,
              ),
            ),
          ],
        ],
      ),
    );
  }
}