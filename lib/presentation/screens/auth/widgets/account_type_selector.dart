import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:sales_app/core/constants/app_dimens.dart';
import 'package:sales_app/core/core.dart';

// Navigation imports (Clean Architecture compliant)
import '../../../router/navigation_extensions.dart';

class AccountTypeSelector extends StatelessWidget {
  const AccountTypeSelector({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Padding(
      padding: AppDimens.paddingHorizontalMd,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          AppDimens.h48,
          AppDimens.h16,
          _buildInfoCard(theme),
          AppDimens.h32,
          _buildHeader(context),
          AppDimens.h32,
          Expanded(
            child: _buildAccountTypeOptions(context: context, theme: theme),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    final theme = Theme.of(context);
    return Text(
      '<PERSON>ui lòng chọn loại tài khoản phù hợp với bạn',
      textAlign: TextAlign.center,
      style: theme.textTheme.bodyMedium?.copyWith(
        color: Colors.white.withValues(alpha: 0.9),
        fontSize: 15,
        fontWeight: FontWeight.bold,
        shadows: [
          Shadow(
            color: Colors.black.withValues(alpha: 0.10),
            offset: const Offset(0, 1),
            blurRadius: 1,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard(ThemeData theme) {
    return ClipRRect(
      borderRadius: AppDimens.borderRadius16,
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
        child: Container(
          padding: AppDimens.paddingVerticalLg,
          decoration: BoxDecoration(
            color: Colors.grey.withValues(alpha: 0.3),
            borderRadius: AppDimens.borderRadius16,
            border: Border.all(color: Colors.white.withOpacity(0.2)),
          ),
          alignment: Alignment.center,
          child: Text(
            'Không vốn, không rủi ro – vẫn có thu nhập!',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: AppColors.textWhite,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  Widget _buildAccountTypeOptions(
      {required BuildContext context, required ThemeData theme}) {
    return ListView(
      physics: const BouncingScrollPhysics(),
      padding: AppDimens.paddingVerticalLg,
      children: [
        _buildAccountTypeOption(
          icon: Icons.person_outline,
          title: 'Cộng tác viên bán hàng',
          subtitle: 'Tham gia mạng lưới bán hàng linh hoạt\n',
          onTap: () {
            // Use Go Router navigation
            context.goToCtvPolicy();
          },
          theme: theme,
        ),
        AppDimens.h24,
        _buildAccountTypeOption(
          icon: Icons.apartment_outlined,
          title: 'Cán bộ KienlongBank',
          subtitle: 'Đăng ký tài khoản dành cho nhân sự nội bộ',
          onTap: () {
            // Navigator.push(context, MaterialPageRoute(builder: (_) => const CBNVScreen()));
          },
          theme: theme,
        ),
      ],
    );
  }

  Widget _buildAccountTypeOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    required ThemeData theme,
  }) {
    return Center(
      child: Container(
        constraints: const BoxConstraints(maxWidth: 400),
        child: GestureDetector(
          onTap: onTap,
          child: Container(
            padding: AppDimens.paddingAllLg,
            decoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.3),
              borderRadius: AppDimens.borderRadius24,
              boxShadow: const [
                BoxShadow(
                  color: Colors.black12,
                  blurRadius: 12,
                  offset: Offset(0, 6),
                ),
              ],
            ),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: Colors.white,
                  size: 32,
                ),
                AppDimens.w16,
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: theme.textTheme.titleSmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 17,
                        ),
                      ),
                      AppDimens.h4,
                      Text(
                        subtitle,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: Colors.white.withValues(alpha: 0.7),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                const Icon(Icons.arrow_forward_ios_rounded,
                    size: 18, color: Colors.white),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
