import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:sales_app/core/theme/app_color.dart';
import 'package:sales_app/presentation/utils/error_message_mapper.dart';
import 'package:sales_app/core/constants/app_dimens.dart';
import 'package:sales_app/core/constants/app_images.dart';
import 'package:sales_app/core/utils/app_logger.dart';
import 'package:sales_app/generated/l10n.dart';
import 'package:sales_app/presentation/controllers/auth_controller.dart';
import 'package:sales_app/presentation/controllers/auth_state.dart';
import '../../widgets/common/app_image.dart';
import '../../widgets/common/common_button.dart';
import '../../widgets/common/common_text_field.dart';
import '../../widgets/loading/loading.dart';
import 'widgets/environment_dropdown.dart';

// Navigation imports (Clean Architecture compliant)
import '../../router/navigation_extensions.dart';

class LoginScreen extends BaseLoadingHookWidget {
  const LoginScreen({super.key});

  @override
  bool isLoading(WidgetRef ref) {
    final authState = ref.watch(authControllerProvider);
    return authState.maybeWhen(
      null, // $default parameter
      loading: () => true,
      orElse: () => false,
    );
  }

  @override
  LoadingType getLoadingType(WidgetRef ref) => LoadingType.login;

  @override
  Widget buildContent(BuildContext context, WidgetRef ref) {
    final emailController = useTextEditingController();
    final passwordController = useTextEditingController();
    final authState = ref.watch(authControllerProvider);
    final authController = ref.read(authControllerProvider.notifier);

    void showErrorSnackBar(BuildContext context, String message) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }

    // Listen to auth state changes
    ref.listen<AuthState>(authControllerProvider, (previous, next) {
      next.when(
        (isLoading, isLoggedIn, user, failure, isEmailValid, isPasswordValid) {
          // Handle default state
        },
        initial: () {},
        loading: () {},
        authenticated: (user) {
          AppLogger.info('User authenticated, navigating to home');
          context.replaceWithHome();
        },
        unauthenticated: (failure) {
          if (failure != null) {
            final errorMessage =
                ErrorMessageMapper.getErrorMessage(context, failure);
            showErrorSnackBar(context, errorMessage);
          }
        },
        error: (failure) {
          final errorMessage =
              ErrorMessageMapper.getErrorMessage(context, failure);
          showErrorSnackBar(context, errorMessage);
        },
      );
    });

    // Handle text field changes for validation
    useEffect(() {
      void onEmailChanged() {
        authController.updateEmailValidation(emailController.text);
      }

      void onPasswordChanged() {
        authController.updatePasswordValidation(passwordController.text);
      }

      emailController.addListener(onEmailChanged);
      passwordController.addListener(onPasswordChanged);

      return () {
        emailController.removeListener(onEmailChanged);
        passwordController.removeListener(onPasswordChanged);
      };
    }, [emailController, passwordController]);

    useEffect(() {
      AppLogger.info('LoginScreen initialized');
      return () => AppLogger.debug('LoginScreen disposed');
    }, []);

    void handleLogin() {
      authController.clearError();
      authController.login(
        email: emailController.text.trim(),
        password: passwordController.text,
      );
    }

    void handleBiometricLogin() {
      authController.biometricLogin();
    }

    final theme = Theme.of(context);
    final screenHeight = MediaQuery.of(context).size.height;

    return Scaffold(
        body: Stack(
      children: [
        Positioned.fill(
          child: AppImages.background.toImage(
            fit: BoxFit.cover,
          ),
        ),
        SafeArea(
          child: SingleChildScrollView(
            padding: AppDimens.paddingHorizontalMd,
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: screenHeight - MediaQuery.of(context).padding.top,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  AppDimens.h48,
                  AppDimens.h16,

                  // Logo
                  SizedBox(
<<<<<<< Updated upstream
                    width: 160,
                    height: 30,
                    child: AppImages.kienlongbankLogo.toImage(
=======
                    width: AppDimens.logoWidthSmall,
                    height: AppDimens.logoHeightSmall,
                    child: Image.asset(
                      'assets/images/kienlongbank_logo.png',
>>>>>>> Stashed changes
                      fit: BoxFit.contain,
                    ),
                  ),

                  AppDimens.h24,

                  // Environment Dropdown
                  const EnvironmentDropdown(
                    isDense: true,
                  ),

                  AppDimens.h32,

                  // Email Field
                  CommonTextField(
                    controller: emailController,
                    label: 'Email',
                    prefixIcon: const Icon(Icons.email),
                    keyboardType: TextInputType.emailAddress,
                    textInputAction: TextInputAction.next,
                    allowClear: true,
                    backgroundColor: Colors.white.withValues(alpha: 0.1),
                    textColor: Colors.white,
                    labelColor: Colors.white,
                    iconColor: Colors.white.withValues(alpha: 0.7),
                    borderColor: Colors.white,
                  ),

                  AppDimens.h16,

                  // Password Field
                  CommonTextField(
                    controller: passwordController,
                    label: S.of(context).password,
                    prefixIcon: const Icon(Icons.lock),
                    isSensitive: true,
                    allowClear: true,
                    textInputAction: TextInputAction.done,
                    onChanged: (_) {},
                    // ignore: deprecated_member_use
                    backgroundColor: Colors.white.withValues(alpha: 0.1),
                    textColor: Colors.white,
                    labelColor: Colors.white,
                    iconColor: Colors.white.withValues(alpha: 0.7),
                    borderColor: Colors.white,
                  ),

                  AppDimens.h16,

                  // Register Link
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () {
                          AppLogger.event('User navigated to register');
                          // Use Go Router navigation
                          context.goToSelectAccountType();
                        },
                        style: TextButton.styleFrom(
                          minimumSize: Size.zero,
                          padding: AppDimens.paddingHorizontalSm,
                        ),
                        child: Text(
                          S.of(context).register_account,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: Colors.white.withValues(alpha: 0.8),
                          ),
                        ),
                      ),
                    ],
                  ),

                  AppDimens.h32,

                  // Login Button
                  CommonButton(
                    title: S.of(context).login,
                    onPressed: handleLogin,
                    enabled: authState.canLogin,
                    backgroundColor: AppColors.buttonColor,
                  ),

                  AppDimens.h24,

                  // Biometric Login
                  Center(
                    child: IconButton(
                      onPressed: handleBiometricLogin,
                      icon: Icon(
                        Icons.fingerprint,
                        color: Colors.white,
                        size: AppDimens.iconXL,
                      ),
                      tooltip: 'Đăng nhập bằng vân tay',
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    ));
  }
}
