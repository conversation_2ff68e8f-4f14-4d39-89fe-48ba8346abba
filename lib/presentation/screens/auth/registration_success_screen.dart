import 'package:flutter/material.dart';
import 'package:sales_app/core/theme/app_color.dart';
import 'package:sales_app/core/constants/app_dimens.dart';
import 'package:sales_app/generated/l10n.dart';
import '../../widgets/common/base_screen.dart';

class RegistrationSuccessScreen extends StatelessWidget {
  final String? fullName;
  final String? branchName;
  final String? position;

  const RegistrationSuccessScreen({
    super.key,
    this.fullName,
    this.branchName,
    this.position,
  });

  @override
  Widget build(BuildContext context) {
    return BaseScreen(
      title: S.of(context).registrationSuccess,
      body: Padding(
        padding: AppDimens.paddingAllXl,
        child: Column(
          children: [
            const Spacer(),
            _buildSuccessMessage(context),
            AppDimens.h32,
            _buildInfoCard(context),
            const Spacer(),
            _buildActionButton(context),
          ],
        ),
      ),
    );
  }

  Widget _buildSuccessMessage(BuildContext context) {
    return Column(
      children: [
        Image.asset(
          height: AppDimens.logoHeightLarge,
          width: AppDimens.logoWidthLarge,
          'assets/images/ic_check_success.png',
          fit: BoxFit.contain,
        ),
        Text(
          S.of(context).registrationSuccess,
          style: TextStyle(
            fontSize: AppDimens.fontXL,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
          textAlign: TextAlign.center,
        ),
        AppDimens.h16,
        Text(
          S.of(context).bankWillContact,
          style: TextStyle(
            fontSize: AppDimens.fontMD,
            color: AppColors.textSecondary,
            height: 1.4,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildInfoCard(BuildContext context) {
    return Container(
      padding: AppDimens.paddingAllLg,
      decoration: BoxDecoration(
        color: AppColors.surfaceColor,
        borderRadius: AppDimens.borderRadius12,
        border: Border.all(
          color: AppColors.primaryColor.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          _buildInfoRow(
            S.of(context).fullName,
            fullName ?? 'Nguyễn Văn A',
            Icons.person_outline,
          ),
          AppDimens.h12,
          _buildInfoRow(
            S.of(context).branch,
            branchName ?? 'Chi nhánh Quận 1',
            Icons.location_on_outlined,
          ),
          AppDimens.h12,
          _buildInfoRow(
            S.of(context).position,
            position ?? S.of(context).newCTV,
            Icons.work_outline,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppColors.primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: AppColors.primaryColor,
            size: 16,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 14,
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 14,
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () {
          Navigator.of(context).popUntil((route) => route.isFirst);
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primaryColor,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          elevation: 0,
        ),
        child: Text(
          S.of(context).close,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}
