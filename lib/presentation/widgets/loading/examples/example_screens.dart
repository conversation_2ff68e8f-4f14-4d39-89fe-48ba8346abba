import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../../../core/constants/app_dimens.dart';
import '../loading_mixin.dart';
import '../adaptive_loading_overlay.dart';

/// Example 1: Silent loading (default behavior)
class SilentLoadingScreen extends BaseLoadingHookWidget {
  const SilentLoadingScreen({super.key});

  @override
  bool isLoading(WidgetRef ref) {
    // Replace with your actual provider
    return false; // Placeholder
  }

  @override
  LoadingType getLoadingType(WidgetRef ref) => LoadingType.loading;

  // No getLoadingMessage() = silent loading (no text)

  @override
  Widget buildContent(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(title: const Text('Silent Loading')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('This screen uses silent loading'),
            AppDimens.h20,
            Text('Only spinner will show, no message'),
          ],
        ),
      ),
    );
  }
}

/// Example 2: Loading with custom message
class MessageLoadingScreen extends BaseLoadingHookWidget {
  const MessageLoadingScreen({super.key});

  @override
  bool isLoading(WidgetRef ref) {
    return false; // Placeholder
  }

  @override
  LoadingType getLoadingType(WidgetRef ref) => LoadingType.loading;

  @override
  String? getLoadingMessage(WidgetRef ref) => 'Đang tải dữ liệu...'; // Custom message

  @override
  Widget buildContent(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(title: const Text('Message Loading')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('This screen shows loading message'),
            AppDimens.h20,
            Text('Spinner + "Đang tải dữ liệu..." will show'),
          ],
        ),
      ),
    );
  }
}

/// Example 3: Dynamic loading messages
class DynamicLoadingScreen extends BaseLoadingHookWidget {
  const DynamicLoadingScreen({super.key});

  @override
  bool isLoading(WidgetRef ref) {
    return false; // Placeholder
  }

  @override
  LoadingType getLoadingType(WidgetRef ref) => LoadingType.saving;

  @override
  String? getLoadingMessage(WidgetRef ref) {
    // Dynamic message based on current operation
    // final operation = ref.watch(someProvider).currentOperation;

    // Example logic:
    const operation = 'saving'; // Placeholder

    switch (operation) {
      case 'saving': return 'Đang lưu dữ liệu...';
      case 'syncing': return 'Đang đồng bộ...';
      case 'uploading': return 'Đang tải lên...';
      case 'processing': return 'Đang xử lý...';
      default: return null; // Silent loading for unknown operations
    }
  }

  @override
  Widget buildContent(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(title: const Text('Dynamic Loading')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('Dynamic loading messages'),
            AppDimens.h20,
            ElevatedButton(
              onPressed: () {
                // Trigger different operations
                // ref.read(someProvider.notifier).save();
              },
              child: const Text('Save (shows "Đang lưu dữ liệu...")'),
            ),
            AppDimens.h8,
            ElevatedButton(
              onPressed: () {
                // ref.read(someProvider.notifier).sync();
              },
              child: const Text('Sync (shows "Đang đồng bộ...")'),
            ),
          ],
        ),
      ),
    );
  }
}

/// Example 4: Using extension method
class ExtensionLoadingScreen extends ConsumerWidget {
  const ExtensionLoadingScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    const isLoading = false; // Placeholder

    return Scaffold(
      appBar: AppBar(title: const Text('Extension Loading')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('Uses extension method'),
            AppDimens.h20,
            Text('Simple and clean approach'),
          ],
        ),
      ),
    ).withLoadingOverlay(
      isLoading: isLoading,
      message: 'Processing with extension...',
    );
  }
}

/// Example 5: Using LoadingMixin for custom control
class MixinLoadingScreen extends ConsumerWidget with LoadingMixin {
  const MixinLoadingScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    const isLoading = false; // Placeholder

    return Scaffold(
      appBar: AppBar(title: const Text('Mixin Loading')),
      body: Stack(
        children: [
          const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('Uses LoadingMixin'),
                AppDimens.h20,
                Text('Custom loading placement'),
              ],
            ),
          ),

          // Custom loading overlay placement
          buildConfiguredLoadingOverlay(
            isLoading: isLoading,
            type: LoadingType.saving,
            customMessage: 'Custom mixin loading...',
          ),
        ],
      ),
    );
  }
}

/// Example 6: Direct AdaptiveLoadingOverlay usage
class DirectLoadingScreen extends StatefulWidget {
  const DirectLoadingScreen({super.key});

  @override
  State<DirectLoadingScreen> createState() => _DirectLoadingScreenState();
}

class _DirectLoadingScreenState extends State<DirectLoadingScreen> {
  bool _isLoading = false;

  void _simulateLoading() {
    setState(() => _isLoading = true);

    // Simulate async operation
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Direct Usage')),
      body: Stack(
        children: [
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('Direct AdaptiveLoadingOverlay usage'),
                AppDimens.h20,
                ElevatedButton(
                  onPressed: _isLoading ? null : _simulateLoading,
                  child: const Text('Start Loading'),
                ),
                AppDimens.h20,
                Text(_isLoading ? 'Loading...' : 'Ready'),
              ],
            ),
          ),

          // Direct usage of AdaptiveLoadingOverlay
          if (_isLoading)
            const AdaptiveLoadingOverlay(
              message: 'Processing your request...',
              backgroundColor: Colors.blue,
              opacity: 0.3,
            ),
        ],
      ),
    );
  }
}

/// Example 7: Login screen with loading
class ExampleLoginScreen extends BaseLoadingHookWidget {
  const ExampleLoginScreen({super.key});

  @override
  bool isLoading(WidgetRef ref) {
    // return ref.watch(authControllerProvider).maybeWhen(
    //   null,
    //   loading: () => true,
    //   orElse: () => false,
    // );
    return false; // Placeholder
  }

  @override
  LoadingType getLoadingType(WidgetRef ref) => LoadingType.login;

  @override
  String? getLoadingMessage(WidgetRef ref) => 'Đang đăng nhập...';

  @override
  Widget buildContent(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(title: const Text('Login Example')),
      body: Padding(
        padding: AppDimens.paddingAllLg,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const TextField(
              decoration: InputDecoration(
                labelText: 'Email',
                border: OutlineInputBorder(),
              ),
            ),
            AppDimens.h16,
            const TextField(
              decoration: InputDecoration(
                labelText: 'Password',
                border: OutlineInputBorder(),
              ),
              obscureText: true,
            ),
            AppDimens.h24,
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  // Trigger login - will show loading overlay
                  // ref.read(authControllerProvider.notifier).login(email, password);
                },
                child: const Text('Login'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
