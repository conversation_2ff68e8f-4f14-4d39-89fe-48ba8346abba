import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Class chứa các giá trị kích thước dùng chung trong ứng dụng
/// Gi<PERSON>p tránh hard-code số trong UI và tăng tính thống nhất
class AppDimens {
  const AppDimens._();

  // Spacing
  static double get spacingXXS => 2.w;
  static double get spacingXS => 4.w;
  static double get spacingSM => 8.w;
  static double get spacingMD => 12.w;
  static double get spacingLG => 16.w;
  static double get spacingXL => 24.w;
  static double get spacingXXL => 32.w;

  // Font sizes
  static double get fontXS => 10.sp;
  static double get fontSM => 12.sp;
  static double get fontMD => 14.sp;
  static double get fontLG => 16.sp;
  static double get fontXL => 18.sp;
  static double get fontXXL => 20.sp;
  static double get fontDisplay => 24.sp;

  // Icon sizes
  static double get iconXS => 16.w;
  static double get iconSM => 20.w;
  static double get iconMD => 24.w;
  static double get iconLG => 32.w;
  static double get iconXL => 40.w;
  static double get iconXXL => 48.w;

  // Border radius
  static BorderRadius get borderRadius4 => BorderRadius.circular(4.r);
  static BorderRadius get borderRadius8 => BorderRadius.circular(8.r);
  static BorderRadius get borderRadius12 => BorderRadius.circular(12.r);
  static BorderRadius get borderRadius16 => BorderRadius.circular(16.r);
  static BorderRadius get borderRadius24 => BorderRadius.circular(24.r);
  static BorderRadius get borderRadius32 => BorderRadius.circular(32.r);

  // Padding
  static EdgeInsets get paddingAllXXS => EdgeInsets.all(spacingXXS);
  static EdgeInsets get paddingAllXS => EdgeInsets.all(spacingXS);
  static EdgeInsets get paddingAllSm => EdgeInsets.all(spacingSM);
  static EdgeInsets get paddingAllMd => EdgeInsets.all(spacingMD);
  static EdgeInsets get paddingAllLg => EdgeInsets.all(spacingLG);
  static EdgeInsets get paddingAllXl => EdgeInsets.all(spacingXL);

  static EdgeInsets get paddingHorizontalXXS => EdgeInsets.symmetric(horizontal: spacingXXS);
  static EdgeInsets get paddingHorizontalXS => EdgeInsets.symmetric(horizontal: spacingXS);
  static EdgeInsets get paddingHorizontalSm => EdgeInsets.symmetric(horizontal: spacingSM);
  static EdgeInsets get paddingHorizontalMd => EdgeInsets.symmetric(horizontal: spacingMD);
  static EdgeInsets get paddingHorizontalLg => EdgeInsets.symmetric(horizontal: spacingLG);
  static EdgeInsets get paddingHorizontalXl => EdgeInsets.symmetric(horizontal: spacingXL);

  static EdgeInsets get paddingVerticalXXS => EdgeInsets.symmetric(vertical: spacingXXS);
  static EdgeInsets get paddingVerticalXS => EdgeInsets.symmetric(vertical: spacingXS);
  static EdgeInsets get paddingVerticalSm => EdgeInsets.symmetric(vertical: spacingSM);
  static EdgeInsets get paddingVerticalMd => EdgeInsets.symmetric(vertical: spacingMD);
  static EdgeInsets get paddingVerticalLg => EdgeInsets.symmetric(vertical: spacingLG);
  static EdgeInsets get paddingVerticalXl => EdgeInsets.symmetric(vertical: spacingXL);

  // Margin
  static EdgeInsets get marginAllXXS => EdgeInsets.all(spacingXXS);
  static EdgeInsets get marginAllXS => EdgeInsets.all(spacingXS);
  static EdgeInsets get marginAllSm => EdgeInsets.all(spacingSM);
  static EdgeInsets get marginAllMd => EdgeInsets.all(spacingMD);
  static EdgeInsets get marginAllLg => EdgeInsets.all(spacingLG);
  static EdgeInsets get marginAllXl => EdgeInsets.all(spacingXL);

  static EdgeInsets get marginHorizontalXXS => EdgeInsets.symmetric(horizontal: spacingXXS);
  static EdgeInsets get marginHorizontalXS => EdgeInsets.symmetric(horizontal: spacingXS);
  static EdgeInsets get marginHorizontalSm => EdgeInsets.symmetric(horizontal: spacingSM);
  static EdgeInsets get marginHorizontalMd => EdgeInsets.symmetric(horizontal: spacingMD);
  static EdgeInsets get marginHorizontalLg => EdgeInsets.symmetric(horizontal: spacingLG);
  static EdgeInsets get marginHorizontalXl => EdgeInsets.symmetric(horizontal: spacingXL);

  static EdgeInsets get marginVerticalXXS => EdgeInsets.symmetric(vertical: spacingXXS);
  static EdgeInsets get marginVerticalXS => EdgeInsets.symmetric(vertical: spacingXS);
  static EdgeInsets get marginVerticalSm => EdgeInsets.symmetric(vertical: spacingSM);
  static EdgeInsets get marginVerticalMd => EdgeInsets.symmetric(vertical: spacingMD);
  static EdgeInsets get marginVerticalLg => EdgeInsets.symmetric(vertical: spacingLG);
  static EdgeInsets get marginVerticalXl => EdgeInsets.symmetric(vertical: spacingXL);

  // Height spacing
  static SizedBox get h2 => SizedBox(height: spacingXXS);
  static SizedBox get h4 => SizedBox(height: spacingXS);
  static SizedBox get h8 => SizedBox(height: spacingSM);
  static SizedBox get h12 => SizedBox(height: spacingMD);
  static SizedBox get h16 => SizedBox(height: spacingLG);
  static SizedBox get h24 => SizedBox(height: spacingXL);
  static SizedBox get h32 => SizedBox(height: spacingXXL);
  static SizedBox get h48 => SizedBox(height: 48.h);

  // Width spacing
  static SizedBox get w2 => SizedBox(width: spacingXXS);
  static SizedBox get w4 => SizedBox(width: spacingXS);
  static SizedBox get w8 => SizedBox(width: spacingSM);
  static SizedBox get w12 => SizedBox(width: spacingMD);
  static SizedBox get w16 => SizedBox(width: spacingLG);
  static SizedBox get w24 => SizedBox(width: spacingXL);
  static SizedBox get w32 => SizedBox(width: spacingXXL);
  static SizedBox get w48 => SizedBox(width: 48.w);

  // Common layout dimensions (responsive)
  static double get buttonHeight => 48.h;
  static double get buttonMinWidth => 120.w;
  static double get appBarHeight => 56.h;
  static double get bottomBarHeight => 72.h;
  static double get bottomSheetHeaderHeight => 72.h;
  static double get toolbarHeight => 56.h;
  static double get dialogWidth => 320.w;

  // Avatar sizes (responsive)
  static double get avatarXs => 24.w;
  static double get avatarSm => 32.w;
  static double get avatarMd => 40.w;
  static double get avatarLg => 56.w;
  static double get avatarXl => 72.w;

  // Container sizes (responsive)
  static double get containerSm => 32.w;
  static double get containerMd => 48.w;
  static double get containerLg => 60.w;
  static double get containerXl => 80.w;

  // Border widths (responsive)
  static double get borderWidthThin => 1.w;
  static double get borderWidthMedium => 2.w;
  static double get borderWidthThick => 4.w;

  // Animation durations (in milliseconds)
  static const int animationFast = 200;
  static const int animationNormal = 300;
  static const int animationSlow = 500;
  static const int animationVerySlow = 1000;

  // Alpha values for transparency
  static const double alphaLow = 0.1;
  static const double alphaMedium = 0.2;
  static const double alphaHigh = 0.5;
  static const double alphaVeryHigh = 0.8;
  static const double alphaNearOpaque = 0.9;

  // Scale factors
  static const double scaleSmall = 0.8;
  static const double scaleNormal = 1.0;
  static const double scaleLarge = 1.5;
  static const double scaleExtraLarge = 2.0;
  static const double scaleHuge = 2.5;

  // Document/Card ratios
  static const double cccdRatio = 8.56 / 5.398; // Tỷ lệ CCCD

  // Notification badge (responsive)
  static double get badgeSize => 16.w;
  static const int maxNotificationDisplay = 9;

  // Camera/Capture specific (responsive)
  static double get captureButtonSize => 80.w;
  static double get captureButtonInnerSize => 64.w;
  static double get captureButtonInnerSizePressed => 32.w;
  static double get captureButtonBorderRadius => 40.r;

  // Logo dimensions (responsive)
  static double get logoWidthSmall => 160.w;
  static double get logoHeightSmall => 30.h;
  static double get logoWidthMedium => 180.w;
  static double get logoHeightMedium => 180.h;
  static double get logoWidthLarge => 130.w;
  static double get logoHeightLarge => 130.h;

  // Additional spacing values
  static double get spacing20 => 20.w;
  static double get spacing72 => 72.w;

  // Additional height spacing
  static SizedBox get h20 => SizedBox(height: spacing20);
  static SizedBox get h72 => SizedBox(height: spacing72);

  // Additional width spacing
  static SizedBox get w20 => SizedBox(width: spacing20);
  static SizedBox get w72 => SizedBox(width: spacing72);

  // Additional font sizes (responsive)
  static double get fontXXXL => 22.sp;
  static double get fontHuge => 28.sp;

  // Default avatar size (responsive)
  static double get defaultAvatarSize => 60.w;

  // Document preview ratios (keep as constants for calculations)
  static const double documentPreviewRatio = 8.56 / 5.398;
  static const double documentPreviewScale = 0.9;
  static const double documentPreviewDivisor = 2.5;

  // Additional border radius values
  static BorderRadius get borderRadius40 => BorderRadius.circular(40.r);

  // Additional container sizes
  static double get containerSize80 => 80.w;

  // Additional blur radius values
  static double get blurRadiusSmall => 4.r;
  static double get blurRadiusMedium => 8.r;
  static double get blurRadiusLarge => 16.r;
  static double get blurRadiusXL => 12.r;

  // Additional offset values
  static Offset get offsetSmall => Offset(0, 2.h);
  static Offset get offsetMedium => Offset(0, 4.h);
  static Offset get offsetLarge => Offset(0, 6.h);

  // Loading indicator radius
  static double get loadingIndicatorRadius => 16.r;

  // Additional border width
  static double get borderWidthMediumThick => 4.w;

  // Grid layout values
  static const int gridCrossAxisCount4 = 4;
  static double get gridCrossAxisSpacing => 8.w;
  static double get gridMainAxisSpacing => 12.h;
  static const double gridChildAspectRatio = 0.85;

  // Elevation values
  static const double elevationLow = 2.0;
  static const double elevationMedium = 4.0;
  static const double elevationHigh = 8.0;

  // Max width constraints
  static double get maxWidthSmall => 400.w;
  static double get maxWidthMedium => 600.w;
  static double get maxWidthLarge => 800.w;

  // Additional padding values
  static EdgeInsets get paddingAll20 => EdgeInsets.all(20.w);

  // Additional container heights
  static double get containerHeight320 => 320.h;
}