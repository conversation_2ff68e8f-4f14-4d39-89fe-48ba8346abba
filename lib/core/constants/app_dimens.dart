import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Class chứa các giá trị kích thước dùng chung trong ứng dụng
/// Gi<PERSON>p tránh hard-code số trong UI và tăng tính thống nhất
class AppDimens {
  const AppDimens._();

  // Spacing
  static double get spacingXXS => 2.w;
  static double get spacingXS => 4.w;
  static double get spacingSM => 8.w;
  static double get spacingMD => 12.w;
  static double get spacingLG => 16.w;
  static double get spacingXL => 24.w;
  static double get spacingXXL => 32.w;

  // Font sizes
  static double get fontXS => 10.sp;
  static double get fontSM => 12.sp;
  static double get fontMD => 14.sp;
  static double get fontLG => 16.sp;
  static double get fontXL => 18.sp;
  static double get fontXXL => 20.sp;
  static double get fontDisplay => 24.sp;

  // Icon sizes
  static double get iconXS => 16.w;
  static double get iconSM => 20.w;
  static double get iconMD => 24.w;
  static double get iconLG => 32.w;
  static double get iconXL => 40.w;
  static double get iconXXL => 48.w;

  // Border radius
  static BorderRadius get borderRadius4 => BorderRadius.circular(4.r);
  static BorderRadius get borderRadius8 => BorderRadius.circular(8.r);
  static BorderRadius get borderRadius12 => BorderRadius.circular(12.r);
  static BorderRadius get borderRadius16 => BorderRadius.circular(16.r);
  static BorderRadius get borderRadius24 => BorderRadius.circular(24.r);
  static BorderRadius get borderRadius32 => BorderRadius.circular(32.r);

  // Padding
  static EdgeInsets get paddingAllXXS => EdgeInsets.all(spacingXXS);
  static EdgeInsets get paddingAllXS => EdgeInsets.all(spacingXS);
  static EdgeInsets get paddingAllSm => EdgeInsets.all(spacingSM);
  static EdgeInsets get paddingAllMd => EdgeInsets.all(spacingMD);
  static EdgeInsets get paddingAllLg => EdgeInsets.all(spacingLG);
  static EdgeInsets get paddingAllXl => EdgeInsets.all(spacingXL);

  static EdgeInsets get paddingHorizontalXXS => EdgeInsets.symmetric(horizontal: spacingXXS);
  static EdgeInsets get paddingHorizontalXS => EdgeInsets.symmetric(horizontal: spacingXS);
  static EdgeInsets get paddingHorizontalSm => EdgeInsets.symmetric(horizontal: spacingSM);
  static EdgeInsets get paddingHorizontalMd => EdgeInsets.symmetric(horizontal: spacingMD);
  static EdgeInsets get paddingHorizontalLg => EdgeInsets.symmetric(horizontal: spacingLG);
  static EdgeInsets get paddingHorizontalXl => EdgeInsets.symmetric(horizontal: spacingXL);

  static EdgeInsets get paddingVerticalXXS => EdgeInsets.symmetric(vertical: spacingXXS);
  static EdgeInsets get paddingVerticalXS => EdgeInsets.symmetric(vertical: spacingXS);
  static EdgeInsets get paddingVerticalSm => EdgeInsets.symmetric(vertical: spacingSM);
  static EdgeInsets get paddingVerticalMd => EdgeInsets.symmetric(vertical: spacingMD);
  static EdgeInsets get paddingVerticalLg => EdgeInsets.symmetric(vertical: spacingLG);
  static EdgeInsets get paddingVerticalXl => EdgeInsets.symmetric(vertical: spacingXL);

  // Margin
  static EdgeInsets get marginAllXXS => EdgeInsets.all(spacingXXS);
  static EdgeInsets get marginAllXS => EdgeInsets.all(spacingXS);
  static EdgeInsets get marginAllSm => EdgeInsets.all(spacingSM);
  static EdgeInsets get marginAllMd => EdgeInsets.all(spacingMD);
  static EdgeInsets get marginAllLg => EdgeInsets.all(spacingLG);
  static EdgeInsets get marginAllXl => EdgeInsets.all(spacingXL);

  static EdgeInsets get marginHorizontalXXS => EdgeInsets.symmetric(horizontal: spacingXXS);
  static EdgeInsets get marginHorizontalXS => EdgeInsets.symmetric(horizontal: spacingXS);
  static EdgeInsets get marginHorizontalSm => EdgeInsets.symmetric(horizontal: spacingSM);
  static EdgeInsets get marginHorizontalMd => EdgeInsets.symmetric(horizontal: spacingMD);
  static EdgeInsets get marginHorizontalLg => EdgeInsets.symmetric(horizontal: spacingLG);
  static EdgeInsets get marginHorizontalXl => EdgeInsets.symmetric(horizontal: spacingXL);

  static EdgeInsets get marginVerticalXXS => EdgeInsets.symmetric(vertical: spacingXXS);
  static EdgeInsets get marginVerticalXS => EdgeInsets.symmetric(vertical: spacingXS);
  static EdgeInsets get marginVerticalSm => EdgeInsets.symmetric(vertical: spacingSM);
  static EdgeInsets get marginVerticalMd => EdgeInsets.symmetric(vertical: spacingMD);
  static EdgeInsets get marginVerticalLg => EdgeInsets.symmetric(vertical: spacingLG);
  static EdgeInsets get marginVerticalXl => EdgeInsets.symmetric(vertical: spacingXL);

  // Height spacing
  static SizedBox get h2 => SizedBox(height: spacingXXS);
  static SizedBox get h4 => SizedBox(height: spacingXS);
  static SizedBox get h8 => SizedBox(height: spacingSM);
  static SizedBox get h12 => SizedBox(height: spacingMD);
  static SizedBox get h16 => SizedBox(height: spacingLG);
  static SizedBox get h24 => SizedBox(height: spacingXL);
  static SizedBox get h32 => SizedBox(height: spacingXXL);
  static SizedBox get h48 => SizedBox(height: 48.h);

  // Width spacing
  static SizedBox get w2 => SizedBox(width: spacingXXS);
  static SizedBox get w4 => SizedBox(width: spacingXS);
  static SizedBox get w8 => SizedBox(width: spacingSM);
  static SizedBox get w12 => SizedBox(width: spacingMD);
  static SizedBox get w16 => SizedBox(width: spacingLG);
  static SizedBox get w24 => SizedBox(width: spacingXL);
  static SizedBox get w32 => SizedBox(width: spacingXXL);
  static SizedBox get w48 => SizedBox(width: 48.w);

  // Common layout dimensions
  static const double buttonHeight = 48.0;
  static const double buttonMinWidth = 120.0;
  static const double appBarHeight = 56.0;
  static const double bottomBarHeight = 72.0;
  static const double bottomSheetHeaderHeight = 72.0;
  static const double toolbarHeight = 56.0;
  static const double dialogWidth = 320.0;

  // Avatar sizes
  static const double avatarXs = 24.0;
  static const double avatarSm = 32.0;
  static const double avatarMd = 40.0;
  static const double avatarLg = 56.0;
  static const double avatarXl = 72.0;

  // Container sizes
  static const double containerSm = 32.0;
  static const double containerMd = 48.0;
  static const double containerLg = 60.0;
  static const double containerXl = 80.0;

  // Border widths
  static const double borderWidthThin = 1.0;
  static const double borderWidthMedium = 2.0;
  static const double borderWidthThick = 4.0;

  // Animation durations (in milliseconds)
  static const int animationFast = 200;
  static const int animationNormal = 300;
  static const int animationSlow = 500;
  static const int animationVerySlow = 1000;

  // Alpha values for transparency
  static const double alphaLow = 0.1;
  static const double alphaMedium = 0.2;
  static const double alphaHigh = 0.5;
  static const double alphaVeryHigh = 0.8;
  static const double alphaNearOpaque = 0.9;

  // Scale factors
  static const double scaleSmall = 0.8;
  static const double scaleNormal = 1.0;
  static const double scaleLarge = 1.5;
  static const double scaleExtraLarge = 2.0;
  static const double scaleHuge = 2.5;

  // Document/Card ratios
  static const double cccdRatio = 8.56 / 5.398; // Tỷ lệ CCCD

  // Notification badge
  static const double badgeSize = 16.0;
  static const int maxNotificationDisplay = 9;

  // Camera/Capture specific
  static const double captureButtonSize = 80.0;
  static const double captureButtonInnerSize = 64.0;
  static const double captureButtonInnerSizePressed = 32.0;
  static const double captureButtonBorderRadius = 40.0;

  // Logo dimensions
  static const double logoWidthSmall = 160.0;
  static const double logoHeightSmall = 30.0;
  static const double logoWidthMedium = 180.0;
  static const double logoHeightMedium = 180.0;
  static const double logoWidthLarge = 130.0;
  static const double logoHeightLarge = 130.0;

  // Additional spacing values
  static double get spacing20 => 20.w;
  static double get spacing72 => 72.w;

  // Additional height spacing
  static SizedBox get h20 => SizedBox(height: spacing20);
  static SizedBox get h72 => SizedBox(height: spacing72);

  // Additional width spacing
  static SizedBox get w20 => SizedBox(width: spacing20);
  static SizedBox get w72 => SizedBox(width: spacing72);

  // Additional font sizes
  static double get fontXXXL => 22.sp;
  static double get fontHuge => 28.sp;

  // Default avatar size
  static const double defaultAvatarSize = 60.0;

  // Document preview ratios
  static const double documentPreviewRatio = 8.56 / 5.398;
  static const double documentPreviewScale = 0.9;
  static const double documentPreviewDivisor = 2.5;
}